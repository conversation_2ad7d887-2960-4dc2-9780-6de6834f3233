import React from "react";
import { Route, RouteProps } from "wouter";

// Extends <PERSON><PERSON><PERSON>'s RouteProps. Component can be any valid React node.
interface AnimatedRouteProps extends Omit<RouteProps, "component"> {
  component: React.ComponentType<any>;
  // Add any additional props you might want to pass to the wrapper or route
}

const AnimatedRoute: React.FC<AnimatedRouteProps> = ({
  component: Component,
  ...rest
}) => {
  return (
    <Route {...rest}>
      {(params) => (
        <div className="animate-fade-in">
          {/* Pass route params to the rendered component */}
          <Component {...params} />
        </div>
      )}
    </Route>
  );
};

export default AnimatedRoute;

import { useEffect } from "react";

interface UseKeyboardNavigationProps {
  onNext?: () => void;
  onPrevious?: () => void;
  onSubmit?: () => void;
  onEscape?: () => void;
  disabled?: boolean;
}

export const useKeyboardNavigation = ({
  onNext,
  onPrevious,
  onSubmit,
  onEscape,
  disabled = false,
}: UseKeyboardNavigationProps) => {
  useEffect(() => {
    if (disabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't interfere with form inputs
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement
      ) {
        return;
      }

      switch (event.key) {
        case "ArrowRight":
        case "ArrowDown":
        case " ": // Spacebar
          event.preventDefault();
          onNext?.();
          break;

        case "ArrowLeft":
        case "ArrowUp":
          event.preventDefault();
          onPrevious?.();
          break;

        case "Enter":
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            onSubmit?.();
          }
          break;

        case "Escape":
          event.preventDefault();
          onEscape?.();
          break;

        default:
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [onNext, onPrevious, onSubmit, onEscape, disabled]);
};

interface UseFlashcardNavigationProps {
  onFlip?: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
  onRateEasy?: () => void;
  onRateMedium?: () => void;
  onRateDifficult?: () => void;
  disabled?: boolean;
}

export const useFlashcardNavigation = ({
  onFlip,
  onNext,
  onPrevious,
  onRateEasy,
  onRateMedium,
  onRateDifficult,
  disabled = false,
}: UseFlashcardNavigationProps) => {
  useEffect(() => {
    if (disabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement
      ) {
        return;
      }

      switch (event.key) {
        case " ": // Spacebar - flip card
          event.preventDefault();
          onFlip?.();
          break;

        case "ArrowRight":
        case "ArrowDown":
          event.preventDefault();
          onNext?.();
          break;

        case "ArrowLeft":
        case "ArrowUp":
          event.preventDefault();
          onPrevious?.();
          break;

        case "1":
          event.preventDefault();
          onRateDifficult?.();
          break;

        case "2":
          event.preventDefault();
          onRateMedium?.();
          break;

        case "3":
          event.preventDefault();
          onRateEasy?.();
          break;

        default:
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [
    onFlip,
    onNext,
    onPrevious,
    onRateEasy,
    onRateMedium,
    onRateDifficult,
    disabled,
  ]);
};

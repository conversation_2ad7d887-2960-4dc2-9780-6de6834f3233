import React, { useEffect } from "react"; // Import useEffect
import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/hooks/useAuth";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { QuizSettingsProvider } from "@/contexts/QuizSettingsContext";
import { ErrorBoundary } from "@/components/layout/ErrorBoundary";

const GlobalEscapeHandler: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    const handleGlobalEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // Attempt to find and close Radix-based dialogs/popovers
        // This is a best-effort approach. Specific components might need their own handling if this isn't sufficient.
        const openDialog = document.querySelector(
          '[role="dialog"][data-state="open"], [role="alertdialog"][data-state="open"]'
        );

        if (openDialog) {
          // Try to find a specific close button. Shadcn typically uses X icon with sr-only "Close"
          const closeButton = 
            openDialog.querySelector('button[aria-label="Close"]') || // Common for Dialog, Sheet
            openDialog.querySelector('button[aria-label="Cancel"]'); // Common for AlertDialog
          
          if (closeButton instanceof HTMLElement) {
            closeButton.click();
            event.preventDefault(); // Prevent other ESC handlers if we found one
            return;
          }
        }
      }
    };

    document.addEventListener('keydown', handleGlobalEsc);
    return () => {
      document.removeEventListener('keydown', handleGlobalEsc);
    };
  }, []);

  return <>{children}</>;
};

createRoot(document.getElementById("root")!).render(
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <GlobalEscapeHandler>
          <QuizSettingsProvider>
            <TooltipProvider>
              <AuthProvider>
                <Toaster />
                <App />
              </AuthProvider>
            </TooltipProvider>
          </QuizSettingsProvider>
        </GlobalEscapeHandler>
      </ThemeProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);
